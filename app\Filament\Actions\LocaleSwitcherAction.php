<?php

namespace App\Filament\Actions;

use Filament\Actions\Action;
use Illuminate\Support\Facades\Session;

class LocaleSwitcherAction extends Action
{
    public static function getDefaultName(): ?string
    {
        return 'locale_switcher';
    }

    protected function setUp(): void
    {
        parent::setUp();

        $this
            ->label('Language')
            ->icon('heroicon-o-language')
            ->color('gray')
            ->button()
            ->action(function (array $data) {
                $locale = $data['locale'];
                Session::put('locale', $locale);
                
                // Redirect to refresh the page with new locale
                return redirect()->to(request()->url() . '?locale=' . $locale);
            })
            ->form([
                \Filament\Forms\Components\Select::make('locale')
                    ->label('Select Language')
                    ->options([
                        'en' => 'English',
                        'am' => 'አማርኛ (Amharic)',
                    ])
                    ->default(app()->getLocale())
                    ->required(),
            ])
            ->modalHeading('Change Language')
            ->modalSubmitActionLabel('Change Language');
    }
}
